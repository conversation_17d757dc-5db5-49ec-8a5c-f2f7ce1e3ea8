"""
Comprehensive Evaluation and Visualization for Tourist Recommendation System
Includes performance metrics, visualizations, and comparison analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from recommendation_system import TouristRecommendationSystem
import warnings
warnings.filterwarnings('ignore')

class RecommendationEvaluator:
    def __init__(self):
        self.rec_system = TouristRecommendationSystem()
        self.evaluation_results = {}
        
    def load_and_prepare_data(self):
        """Load and prepare evaluation data"""
        print("Loading evaluation data...")
        
        if not self.rec_system.load_data():
            return False
            
        # Create evaluation splits
        self.train_reviews, self.test_reviews = train_test_split(
            self.rec_system.reviews_df, 
            test_size=0.2, 
            random_state=42
        )
        
        print(f"Training reviews: {len(self.train_reviews)}")
        print(f"Test reviews: {len(self.test_reviews)}")
        return True
    
    def evaluate_rating_prediction_accuracy(self):
        """Evaluate rating prediction accuracy using collaborative filtering"""
        print("\n=== Evaluating Rating Prediction Accuracy ===")
        
        # Build user-item matrix from training data
        train_matrix = self.train_reviews.pivot_table(
            index='UserID', 
            columns='DestinationID', 
            values='Rating',
            fill_value=0
        )
        
        # Predict ratings for test set
        predictions = []
        actuals = []
        
        for _, row in self.test_reviews.iterrows():
            user_id = row['UserID']
            dest_id = row['DestinationID']
            actual_rating = row['Rating']
            
            # Simple prediction: average rating for destination
            if dest_id in train_matrix.columns:
                dest_ratings = train_matrix[dest_id]
                non_zero_ratings = dest_ratings[dest_ratings > 0]
                if len(non_zero_ratings) > 0:
                    predicted_rating = non_zero_ratings.mean()
                else:
                    predicted_rating = 3.0  # Default rating
            else:
                predicted_rating = 3.0
            
            predictions.append(predicted_rating)
            actuals.append(actual_rating)
        
        # Calculate metrics
        rmse = np.sqrt(mean_squared_error(actuals, predictions))
        mae = mean_absolute_error(actuals, predictions)
        
        self.evaluation_results['rating_prediction'] = {
            'rmse': rmse,
            'mae': mae,
            'predictions': predictions,
            'actuals': actuals
        }
        
        print(f"RMSE: {rmse:.3f}")
        print(f"MAE: {mae:.3f}")
        
        return rmse, mae
    
    def evaluate_recommendation_diversity(self):
        """Evaluate diversity of recommendations"""
        print("\n=== Evaluating Recommendation Diversity ===")
        
        diversity_scores = []
        novelty_scores = []
        
        # Sample users for evaluation
        sample_users = self.rec_system.user_item_matrix.index[:20]
        
        for user_id in sample_users:
            # Get recommendations
            recs = self.rec_system.get_hybrid_recommendations(
                user_id=user_id,
                n_recommendations=10
            )
            
            if recs:
                # Calculate diversity (unique categories)
                states = set([r.get('State', '') for r in recs])
                types = set([r.get('Type', '') for r in recs])
                
                # Diversity score: ratio of unique categories to total recommendations
                diversity = (len(states) + len(types)) / (2 * len(recs))
                diversity_scores.append(diversity)
                
                # Novelty score: average popularity (lower = more novel)
                popularities = [r.get('Popularity', 8.0) for r in recs]
                novelty = 10.0 - np.mean(popularities)  # Invert popularity
                novelty_scores.append(novelty)
        
        avg_diversity = np.mean(diversity_scores) if diversity_scores else 0
        avg_novelty = np.mean(novelty_scores) if novelty_scores else 0
        
        self.evaluation_results['diversity'] = {
            'diversity_score': avg_diversity,
            'novelty_score': avg_novelty,
            'diversity_scores': diversity_scores,
            'novelty_scores': novelty_scores
        }
        
        print(f"Average Diversity Score: {avg_diversity:.3f}")
        print(f"Average Novelty Score: {avg_novelty:.3f}")
        
        return avg_diversity, avg_novelty
    
    def evaluate_recommendation_coverage(self):
        """Evaluate catalog coverage of recommendations"""
        print("\n=== Evaluating Recommendation Coverage ===")
        
        recommended_destinations = set()
        total_recommendations = 0
        
        # Sample users for evaluation
        sample_users = self.rec_system.user_item_matrix.index[:50]
        
        for user_id in sample_users:
            recs = self.rec_system.get_hybrid_recommendations(
                user_id=user_id,
                n_recommendations=5
            )
            
            for rec in recs:
                recommended_destinations.add(rec['DestinationID'])
                total_recommendations += 1
        
        total_destinations = len(self.rec_system.destinations_df)
        coverage = len(recommended_destinations) / total_destinations
        
        self.evaluation_results['coverage'] = {
            'coverage_score': coverage,
            'recommended_destinations': len(recommended_destinations),
            'total_destinations': total_destinations,
            'total_recommendations': total_recommendations
        }
        
        print(f"Catalog Coverage: {coverage:.3f}")
        print(f"Recommended Destinations: {len(recommended_destinations)}/{total_destinations}")
        
        return coverage
    
    def compare_recommendation_methods(self):
        """Compare different recommendation methods"""
        print("\n=== Comparing Recommendation Methods ===")
        
        methods = ['content', 'collaborative', 'cluster', 'hybrid']
        method_performance = {}
        
        sample_users = self.rec_system.user_item_matrix.index[:10]
        
        for method in methods:
            diversities = []
            coverages = []
            
            for user_id in sample_users:
                if method == 'content':
                    recs = self.rec_system.get_content_recommendations(1, 5)  # Use dest_id=1
                elif method == 'collaborative':
                    recs = self.rec_system.get_collaborative_recommendations(user_id, 5)
                elif method == 'cluster':
                    recs = self.rec_system.get_cluster_recommendations({'type': 'City', 'state': 'Goa'}, 5)
                else:  # hybrid
                    recs = self.rec_system.get_hybrid_recommendations(
                        user_id=user_id,
                        user_preferences={'type': 'City', 'state': 'Goa'},
                        n_recommendations=5
                    )
                
                if recs:
                    # Calculate diversity
                    states = set([r.get('State', '') for r in recs])
                    types = set([r.get('Type', '') for r in recs])
                    diversity = (len(states) + len(types)) / (2 * len(recs))
                    diversities.append(diversity)
                    
                    # Calculate coverage
                    coverage = len(recs) / 5.0
                    coverages.append(coverage)
            
            method_performance[method] = {
                'avg_diversity': np.mean(diversities) if diversities else 0,
                'avg_coverage': np.mean(coverages) if coverages else 0
            }
        
        self.evaluation_results['method_comparison'] = method_performance
        
        for method, metrics in method_performance.items():
            print(f"{method.capitalize()}: Diversity={metrics['avg_diversity']:.3f}, Coverage={metrics['avg_coverage']:.3f}")
        
        return method_performance
    
    def create_visualizations(self):
        """Create comprehensive visualizations"""
        print("\n=== Creating Visualizations ===")
        
        # Set up the plotting style
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Tourist Recommendation System Evaluation', fontsize=16, fontweight='bold')
        
        # 1. Rating Prediction Accuracy
        if 'rating_prediction' in self.evaluation_results:
            ax = axes[0, 0]
            predictions = self.evaluation_results['rating_prediction']['predictions']
            actuals = self.evaluation_results['rating_prediction']['actuals']
            
            ax.scatter(actuals, predictions, alpha=0.6, color='blue')
            ax.plot([1, 5], [1, 5], 'r--', lw=2)
            ax.set_xlabel('Actual Ratings')
            ax.set_ylabel('Predicted Ratings')
            ax.set_title('Rating Prediction Accuracy')
            ax.grid(True, alpha=0.3)
        
        # 2. Diversity Distribution
        if 'diversity' in self.evaluation_results:
            ax = axes[0, 1]
            diversity_scores = self.evaluation_results['diversity']['diversity_scores']
            ax.hist(diversity_scores, bins=15, alpha=0.7, color='green', edgecolor='black')
            ax.set_xlabel('Diversity Score')
            ax.set_ylabel('Frequency')
            ax.set_title('Recommendation Diversity Distribution')
            ax.grid(True, alpha=0.3)
        
        # 3. Method Comparison
        if 'method_comparison' in self.evaluation_results:
            ax = axes[0, 2]
            methods = list(self.evaluation_results['method_comparison'].keys())
            diversities = [self.evaluation_results['method_comparison'][m]['avg_diversity'] for m in methods]
            
            bars = ax.bar(methods, diversities, color=['skyblue', 'lightcoral', 'lightgreen', 'gold'])
            ax.set_ylabel('Average Diversity Score')
            ax.set_title('Method Comparison: Diversity')
            ax.grid(True, alpha=0.3)
            
            # Add value labels on bars
            for bar, value in zip(bars, diversities):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{value:.3f}', ha='center', va='bottom')
        
        # 4. Destination Type Distribution
        ax = axes[1, 0]
        type_counts = self.rec_system.destinations_df['Type'].value_counts()
        ax.pie(type_counts.values, labels=type_counts.index, autopct='%1.1f%%', startangle=90)
        ax.set_title('Destination Type Distribution')
        
        # 5. State Distribution
        ax = axes[1, 1]
        state_counts = self.rec_system.destinations_df['State'].value_counts().head(8)
        ax.bar(range(len(state_counts)), state_counts.values, color='orange')
        ax.set_xticks(range(len(state_counts)))
        ax.set_xticklabels(state_counts.index, rotation=45, ha='right')
        ax.set_ylabel('Number of Destinations')
        ax.set_title('Top States by Destination Count')
        ax.grid(True, alpha=0.3)
        
        # 6. Rating Distribution
        ax = axes[1, 2]
        rating_counts = self.rec_system.reviews_df['Rating'].value_counts().sort_index()
        ax.bar(rating_counts.index, rating_counts.values, color='purple', alpha=0.7)
        ax.set_xlabel('Rating')
        ax.set_ylabel('Frequency')
        ax.set_title('Rating Distribution')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('recommendation_evaluation.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("Visualizations saved as 'recommendation_evaluation.png'")
    
    def generate_evaluation_report(self):
        """Generate comprehensive evaluation report"""
        print("\n" + "="*60)
        print("TOURIST RECOMMENDATION SYSTEM EVALUATION REPORT")
        print("="*60)
        
        # System Overview
        print(f"\n📊 SYSTEM OVERVIEW")
        print(f"Total Destinations: {len(self.rec_system.destinations_df)}")
        print(f"Total Users: {len(self.rec_system.user_item_matrix.index) if self.rec_system.user_item_matrix is not None else 0}")
        print(f"Total Reviews: {len(self.rec_system.reviews_df)}")
        
        # Performance Metrics
        if 'rating_prediction' in self.evaluation_results:
            print(f"\n🎯 RATING PREDICTION PERFORMANCE")
            print(f"RMSE: {self.evaluation_results['rating_prediction']['rmse']:.3f}")
            print(f"MAE: {self.evaluation_results['rating_prediction']['mae']:.3f}")
        
        if 'diversity' in self.evaluation_results:
            print(f"\n🌈 RECOMMENDATION QUALITY")
            print(f"Diversity Score: {self.evaluation_results['diversity']['diversity_score']:.3f}")
            print(f"Novelty Score: {self.evaluation_results['diversity']['novelty_score']:.3f}")
        
        if 'coverage' in self.evaluation_results:
            print(f"Coverage Score: {self.evaluation_results['coverage']['coverage_score']:.3f}")
        
        # Method Comparison
        if 'method_comparison' in self.evaluation_results:
            print(f"\n🔄 METHOD COMPARISON")
            for method, metrics in self.evaluation_results['method_comparison'].items():
                print(f"{method.capitalize():>15}: Diversity={metrics['avg_diversity']:.3f}, Coverage={metrics['avg_coverage']:.3f}")
        
        print("\n" + "="*60)
    
    def run_full_evaluation(self):
        """Run complete evaluation pipeline"""
        print("Starting comprehensive evaluation...")
        
        # Load data and train models
        if not self.load_and_prepare_data():
            print("Failed to load data")
            return
        
        if not self.rec_system.train_all_models():
            print("Failed to train models")
            return
        
        # Run evaluations
        self.evaluate_rating_prediction_accuracy()
        self.evaluate_recommendation_diversity()
        self.evaluate_recommendation_coverage()
        self.compare_recommendation_methods()
        
        # Create visualizations
        self.create_visualizations()
        
        # Generate report
        self.generate_evaluation_report()
        
        print("\n✅ Evaluation completed successfully!")

if __name__ == "__main__":
    evaluator = RecommendationEvaluator()
    evaluator.run_full_evaluation()
