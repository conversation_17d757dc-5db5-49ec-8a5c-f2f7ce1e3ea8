"""
Setup script to create a compatible environment for the Tourist Rating Predictor
This script helps fix NumPy compatibility issues
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully!")
            return True
        else:
            print(f"❌ {description} failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error during {description}: {str(e)}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 7:
        print("✅ Python version is compatible!")
        return True
    else:
        print("❌ Python 3.7+ is required!")
        return False

def create_virtual_environment():
    """Create a virtual environment"""
    env_name = "tourist_env"
    
    if os.path.exists(env_name):
        print(f"📁 Virtual environment '{env_name}' already exists")
        return True
    
    return run_command(f"python -m venv {env_name}", f"Creating virtual environment '{env_name}'")

def install_dependencies():
    """Install compatible dependencies"""
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install numpy==1.24.3", "Installing NumPy 1.24.3"),
        ("pip install pandas==1.5.3", "Installing Pandas 1.5.3"),
        ("pip install scikit-learn==1.3.2", "Installing scikit-learn 1.3.2"),
        ("pip install xgboost==1.7.6", "Installing XGBoost 1.7.6"),
        ("pip install streamlit==1.28.1", "Installing Streamlit"),
        ("pip install textblob joblib matplotlib seaborn", "Installing additional packages")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True

def test_imports():
    """Test if all required packages can be imported"""
    packages = [
        "numpy", "pandas", "sklearn", "xgboost", 
        "streamlit", "textblob", "joblib", "matplotlib", "seaborn"
    ]
    
    print("\n🧪 Testing package imports...")
    failed_imports = []
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError as e:
            print(f"❌ {package}: {str(e)}")
            failed_imports.append(package)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ All packages imported successfully!")
        return True

def main():
    """Main setup function"""
    print("🏖️ Tourist Rating Predictor - Environment Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        print("\n💡 Please install Python 3.7 or higher and try again.")
        return
    
    print("\n📋 Setup Options:")
    print("1. Create new virtual environment (Recommended)")
    print("2. Install in current environment")
    print("3. Just show manual instructions")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        print("\n🔧 Setting up new virtual environment...")
        
        if create_virtual_environment():
            env_name = "tourist_env"
            activation_cmd = f"{env_name}\\Scripts\\activate" if os.name == 'nt' else f"source {env_name}/bin/activate"
            
            print(f"\n✅ Virtual environment created successfully!")
            print(f"\n📝 Next steps:")
            print(f"1. Activate the environment: {activation_cmd}")
            print(f"2. Install dependencies: python setup_environment.py")
            print(f"3. Choose option 2 when prompted")
            print(f"4. Run the app: streamlit run app.py")
    
    elif choice == "2":
        print("\n🔧 Installing dependencies in current environment...")
        
        if install_dependencies():
            if test_imports():
                print("\n🎉 Setup completed successfully!")
                print("\n📝 Next steps:")
                print("1. Train the model (optional): python train_model.py")
                print("2. Run the app: streamlit run app.py")
                print("3. Open browser to: http://localhost:8501")
            else:
                print("\n❌ Some packages failed to import. Please check the errors above.")
        else:
            print("\n❌ Failed to install dependencies. Please check the errors above.")
    
    elif choice == "3":
        print("\n📝 Manual Setup Instructions:")
        print("\n🔧 Option A: Create Virtual Environment")
        print("1. python -m venv tourist_env")
        print("2. tourist_env\\Scripts\\activate  # Windows")
        print("   source tourist_env/bin/activate  # Mac/Linux")
        print("3. pip install numpy==1.24.3 pandas==1.5.3 scikit-learn==1.3.2")
        print("4. pip install xgboost==1.7.6 streamlit textblob joblib")
        print("5. streamlit run app.py")
        
        print("\n🔧 Option B: Fix Current Environment")
        print("1. pip install numpy<2.0 --force-reinstall")
        print("2. pip install pandas<2.0 scikit-learn<1.4 xgboost<2.0")
        print("3. pip install streamlit textblob joblib")
        print("4. streamlit run app.py")
        
        print("\n💡 The app works with mock predictions even without training the model!")
    
    else:
        print("❌ Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()
