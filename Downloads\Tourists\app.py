import streamlit as st
import pandas as pd
import numpy as np
import pickle
import joblib
try:
    from textblob import TextBlob
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False
    st.warning("TextBlob not available. Using simple sentiment analysis.")

import warnings
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="Tourist Rating Predictor",
    page_icon="🏖️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #ff7f0e;
        margin-bottom: 1rem;
    }
    .prediction-box {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 10px;
        border-left: 5px solid #1f77b4;
        margin: 1rem 0;
    }
    .feature-box {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Title
st.markdown('<h1 class="main-header">🏖️ Tourist Destination Rating Predictor</h1>', unsafe_allow_html=True)
st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #666;">Predict tourist ratings using advanced machine learning</p>', unsafe_allow_html=True)

# Load data for reference
@st.cache_data
def load_data():
    try:
        df = pd.read_csv('cleaned_data.csv')
        return df
    except FileNotFoundError:
        st.error("cleaned_data.csv file not found. Please ensure the file is in the correct directory.")
        return None

# Function to calculate sentiment score
def get_sentiment_score(text):
    """Calculate sentiment score using TextBlob or simple method"""
    if pd.isna(text) or text == "":
        return 0.0

    if TEXTBLOB_AVAILABLE:
        blob = TextBlob(str(text))
        return blob.sentiment.polarity
    else:
        # Simple sentiment analysis based on keywords
        text = str(text).lower()
        positive_words = ['amazing', 'great', 'excellent', 'wonderful', 'fantastic', 'love', 'loved', 'beautiful', 'perfect', 'awesome']
        negative_words = ['bad', 'terrible', 'awful', 'horrible', 'hate', 'worst', 'disappointing', 'poor', 'boring']

        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)

        if positive_count > negative_count:
            return 0.5
        elif negative_count > positive_count:
            return -0.5
        else:
            return 0.0

# Function to create model features
def create_model_features(user_inputs):
    """Create features in the same format as the trained model"""
    
    # Create a base feature vector with all possible one-hot encoded features
    features = {
        'Popularity': user_inputs['popularity'],
        'ExperienceRating': user_inputs['experience_rating'],
        'NumberOfAdults': user_inputs['num_adults'],
        'NumberOfChildren': user_inputs['num_children'],
        'SentimentScore': get_sentiment_score(user_inputs['review_text']),
        
        # State features (one-hot encoded)
        'State_Goa': 1 if user_inputs['state'] == 'Goa' else 0,
        'State_Jammu and Kashmir': 1 if user_inputs['state'] == 'Jammu and Kashmir' else 0,
        'State_Kerala': 1 if user_inputs['state'] == 'Kerala' else 0,
        'State_Rajasthan': 1 if user_inputs['state'] == 'Rajasthan' else 0,
        'State_Uttar Pradesh': 1 if user_inputs['state'] == 'Uttar Pradesh' else 0,
        
        # Type features (one-hot encoded)
        'Type_Adventure': 1 if user_inputs['destination_type'] == 'Adventure' else 0,
        'Type_Beach': 1 if user_inputs['destination_type'] == 'Beach' else 0,
        'Type_City': 1 if user_inputs['destination_type'] == 'City' else 0,
        'Type_Historical': 1 if user_inputs['destination_type'] == 'Historical' else 0,
        'Type_Nature': 1 if user_inputs['destination_type'] == 'Nature' else 0,
        
        # Best time to visit features (one-hot encoded)
        'BestTimeToVisit_Apr-Jun': 1 if user_inputs['best_time'] == 'Apr-Jun' else 0,
        'BestTimeToVisit_Nov-Feb': 1 if user_inputs['best_time'] == 'Nov-Feb' else 0,
        'BestTimeToVisit_Nov-Mar': 1 if user_inputs['best_time'] == 'Nov-Mar' else 0,
        'BestTimeToVisit_Oct-Mar': 1 if user_inputs['best_time'] == 'Oct-Mar' else 0,
        'BestTimeToVisit_Sep-Mar': 1 if user_inputs['best_time'] == 'Sep-Mar' else 0,
        
        # Gender features (one-hot encoded)
        'Gender_Female': 1 if user_inputs['gender'] == 'Female' else 0,
        'Gender_Male': 1 if user_inputs['gender'] == 'Male' else 0,
        
        # Preferences features (one-hot encoded)
        'Preferences_Beaches, Historical': 1 if user_inputs['preferences'] == 'Beaches, Historical' else 0,
        'Preferences_City, Historical': 1 if user_inputs['preferences'] == 'City, Historical' else 0,
        'Preferences_Nature, Adventure': 1 if user_inputs['preferences'] == 'Nature, Adventure' else 0,
    }
    
    # Selected features based on the notebook analysis
    selected_features = [
        'Type_City', 'Type_Beach', 'State_Uttar Pradesh', 'Gender_Male', 
        'Type_Historical', 'State_Rajasthan', 'State_Goa', 
        'Preferences_City, Historical', 'State_Kerala', 
        'Preferences_Nature, Adventure', 'State_Jammu and Kashmir', 
        'Preferences_Beaches, Historical', 'NumberOfAdults', 
        'SentimentScore', 'Gender_Female', 'NumberOfChildren', 'Popularity'
    ]
    
    # Create feature vector with only selected features
    feature_vector = [features[feature] for feature in selected_features]
    
    return np.array(feature_vector).reshape(1, -1), selected_features

# Load the trained model
@st.cache_resource
def load_model():
    """Load the trained XGBoost model"""
    try:
        model = joblib.load('xgboost_model.pkl')
        feature_names = joblib.load('feature_names.pkl')
        return model, feature_names
    except FileNotFoundError:
        st.warning("⚠️ Trained model not found. Using mock predictions. Run 'python train_model.py' to train the model.")
        return None, None

def predict_rating(features, model=None):
    """
    Predict rating using the trained XGBoost model
    """
    if model is not None:
        # Use actual trained model
        prediction = model.predict(features) + 1  # Add 1 to convert from 0-indexed
        return int(prediction[0])
    else:
        # Fallback to mock prediction
        feature_sum = np.sum(features)
        if feature_sum > 10:
            return 5
        elif feature_sum > 8:
            return 4
        elif feature_sum > 6:
            return 3
        elif feature_sum > 4:
            return 2
        else:
            return 1

# Load data and model
df = load_data()
model, model_feature_names = load_model()

if df is not None:
    # Sidebar for user inputs
    st.sidebar.markdown('<h2 class="sub-header">🎯 Input Features</h2>', unsafe_allow_html=True)
    
    # Get unique values from the dataset
    states = df['State'].unique().tolist()
    destination_types = df['Type'].unique().tolist()
    preferences_options = df['Preferences'].unique().tolist()
    best_time_options = df['BestTimeToVisit'].unique().tolist()
    
    # User inputs
    user_inputs = {}
    
    with st.sidebar:
        st.markdown("### 📍 Destination Information")
        user_inputs['state'] = st.selectbox("State", states)
        user_inputs['destination_type'] = st.selectbox("Destination Type", destination_types)
        user_inputs['best_time'] = st.selectbox("Best Time to Visit", best_time_options)
        user_inputs['popularity'] = st.slider("Destination Popularity", 7.5, 9.5, 8.5, 0.1)
        
        st.markdown("### 👤 User Information")
        user_inputs['gender'] = st.selectbox("Gender", ['Male', 'Female'])
        user_inputs['preferences'] = st.selectbox("Travel Preferences", preferences_options)
        user_inputs['num_adults'] = st.number_input("Number of Adults", 1, 5, 2)
        user_inputs['num_children'] = st.number_input("Number of Children", 0, 5, 1)
        
        st.markdown("### 📝 Experience Information")
        user_inputs['experience_rating'] = st.slider("Previous Experience Rating", 1, 5, 3)
        user_inputs['review_text'] = st.text_area("Review Text (for sentiment analysis)", 
                                                 "This place was amazing!")
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown('<h2 class="sub-header">📊 Feature Analysis</h2>', unsafe_allow_html=True)
        
        # Create features
        features, feature_names = create_model_features(user_inputs)
        
        # Display feature values
        feature_df = pd.DataFrame({
            'Feature': feature_names,
            'Value': features[0]
        })
        
        st.dataframe(feature_df, use_container_width=True)
        
        # Sentiment analysis
        sentiment_score = get_sentiment_score(user_inputs['review_text'])
        st.markdown(f"**Sentiment Score:** {sentiment_score:.3f}")
        if sentiment_score > 0.1:
            st.success("😊 Positive sentiment detected!")
        elif sentiment_score < -0.1:
            st.error("😞 Negative sentiment detected!")
        else:
            st.info("😐 Neutral sentiment detected!")
    
    with col2:
        st.markdown('<h2 class="sub-header">🎯 Prediction</h2>', unsafe_allow_html=True)
        
        if st.button("🔮 Predict Rating", type="primary", use_container_width=True):
            # Make prediction
            predicted_rating = predict_rating(features, model)

            # Display prediction
            st.markdown(f"""
            <div class="prediction-box">
                <h3 style="text-align: center; margin: 0;">Predicted Rating</h3>
                <h1 style="text-align: center; color: #1f77b4; margin: 10px 0;">
                    {predicted_rating} ⭐
                </h1>
                <p style="text-align: center; margin: 0;">
                    {"Excellent!" if predicted_rating >= 4 else 
                     "Good!" if predicted_rating >= 3 else 
                     "Average" if predicted_rating >= 2 else "Poor"}
                </p>
            </div>
            """, unsafe_allow_html=True)
            
            # Rating interpretation
            rating_meanings = {
                5: "⭐⭐⭐⭐⭐ Outstanding experience expected!",
                4: "⭐⭐⭐⭐ Very good experience expected!",
                3: "⭐⭐⭐ Good experience expected!",
                2: "⭐⭐ Fair experience expected.",
                1: "⭐ Below average experience expected."
            }
            
            st.info(rating_meanings[predicted_rating])
    
    # Additional information
    st.markdown('<h2 class="sub-header">📈 Model Information</h2>', unsafe_allow_html=True)
    
    col3, col4, col5 = st.columns(3)
    
    with col3:
        st.markdown("""
        <div class="feature-box">
            <h4>🤖 Model Type</h4>
            <p>XGBoost Classifier with feature selection</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown("""
        <div class="feature-box">
            <h4>🎯 Accuracy</h4>
            <p>62.31% on test data</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col5:
        st.markdown("""
        <div class="feature-box">
            <h4>📊 Features</h4>
            <p>17 selected features</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Instructions for model deployment
    st.markdown("---")
    if model is None:
        st.markdown("### 🚀 To Use Real Model:")
        st.markdown("""
        1. Run the training script: `python train_model.py`
        2. This will create `xgboost_model.pkl` and `feature_names.pkl`
        3. Refresh the app to use the trained model
        4. Install dependencies: `pip install -r requirements.txt`
        """)
        st.info("💡 Currently using mock predictions. Train the model for accurate results!")
    else:
        st.success("✅ Using trained XGBoost model for predictions!")
        st.markdown("### 📊 Model Features:")
        st.write(f"Model trained with {len(model_feature_names)} features:")
        st.write(model_feature_names)

else:
    st.error("Unable to load data. Please check if 'cleaned_data.csv' exists in the current directory.")
