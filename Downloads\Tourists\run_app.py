"""
<PERSON><PERSON><PERSON> to set up and run the Tourist Rating Predictor app
"""

import os
import subprocess
import sys

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = [
        'streamlit', 'pandas', 'numpy', 'scikit-learn',
        'xgboost', 'textblob', 'joblib'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Try these solutions:")
        print("1. Use pre-built wheels: pip install --only-binary=all -r requirements.txt")
        print("2. Or install individually: pip install streamlit pandas numpy scikit-learn")
        print("3. If still failing, try: pip install --upgrade pip setuptools wheel")
        return False

    print("✅ All required packages are installed!")
    return True

def check_data_files():
    """Check if required data files exist"""
    required_files = ['cleaned_data.csv']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required data files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ All required data files found!")
    return True

def check_model_files():
    """Check if model files exist"""
    model_files = ['xgboost_model.pkl', 'feature_names.pkl']
    missing_files = []
    
    for file in model_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("⚠️  Model files not found:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ Model files found!")
    return True

def train_model():
    """Train the model if it doesn't exist"""
    print("\n🤖 Training the XGBoost model...")
    try:
        result = subprocess.run([sys.executable, 'train_model.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Model training completed successfully!")
            return True
        else:
            print("❌ Model training failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error during model training: {str(e)}")
        return False

def run_streamlit():
    """Run the Streamlit app"""
    print("\n🚀 Starting Streamlit app...")
    try:
        subprocess.run([sys.executable, '-m', 'streamlit', 'run', 'app.py'])
    except KeyboardInterrupt:
        print("\n👋 App stopped by user")
    except Exception as e:
        print(f"❌ Error running Streamlit: {str(e)}")

def main():
    """Main setup and run function"""
    print("🏖️  Tourist Rating Predictor Setup")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("\n💡 Please install dependencies first:")
        print("   pip install -r requirements.txt")
        return
    
    # Check data files
    if not check_data_files():
        print("\n💡 Please ensure cleaned_data.csv is in the current directory")
        return
    
    # Check model files
    if not check_model_files():
        print("\n🤖 Model files not found. Training model...")
        if not train_model():
            print("\n❌ Failed to train model. You can still run the app with mock predictions.")
            response = input("Continue anyway? (y/n): ").lower().strip()
            if response != 'y':
                return
    
    # Run the app
    print("\n" + "=" * 40)
    print("🎉 Setup complete! Starting the app...")
    print("💡 The app will open in your default browser")
    print("💡 Press Ctrl+C to stop the app")
    print("=" * 40)
    
    run_streamlit()

if __name__ == "__main__":
    main()
