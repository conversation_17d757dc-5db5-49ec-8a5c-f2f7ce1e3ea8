import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
from sklearn.decomposition import TruncatedSVD
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import NearestNeighbors
import warnings
warnings.filterwarnings('ignore')

class TouristRecommendationSystem:
    def __init__(self):
        self.content_model = None
        self.collaborative_model = None
        self.clustering_model = None
        self.scaler = StandardScaler()
        self.destinations_df = None
        self.users_df = None
        self.reviews_df = None
        self.user_item_matrix = None
        
    def load_data(self):
        """Load and prepare data for recommendations"""
        try:
            # Load datasets from the datasets folder
            self.destinations_df = pd.read_csv('datasets/Expanded_Destinations.csv')
            self.users_df = pd.read_csv('datasets/Final_Updated_Expanded_Users.csv')
            self.reviews_df = pd.read_csv('datasets/Final_Updated_Expanded_Reviews.csv')
            
            print(f"Loaded {len(self.destinations_df)} destinations")
            print(f"Loaded {len(self.users_df)} users")
            print(f"Loaded {len(self.reviews_df)} reviews")
            
            return True
        except FileNotFoundError as e:
            print(f"Error loading data: {e}")
            return False
    
    def build_content_based_model(self):
        """Build content-based recommendation model"""
        # Create content features combining multiple attributes
        self.destinations_df['content_features'] = (
            self.destinations_df['State'].fillna('') + ' ' +
            self.destinations_df['Type'].fillna('') + ' ' +
            self.destinations_df['BestTimeToVisit'].fillna('')
        )
        
        # Create TF-IDF vectors for content similarity
        tfidf = TfidfVectorizer(stop_words='english', max_features=100)
        self.content_matrix = tfidf.fit_transform(self.destinations_df['content_features'])
        
        # Calculate cosine similarity matrix
        self.content_similarity = cosine_similarity(self.content_matrix)
        
        print("Content-based model built successfully")
    
    def build_collaborative_model(self):
        """Build collaborative filtering model"""
        # Create user-item matrix
        self.user_item_matrix = self.reviews_df.pivot_table(
            index='UserID', 
            columns='DestinationID', 
            values='Rating',
            fill_value=0
        )
        
        # Use SVD for dimensionality reduction
        n_components = min(50, min(self.user_item_matrix.shape) - 1)
        svd = TruncatedSVD(n_components=n_components, random_state=42)
        self.user_factors = svd.fit_transform(self.user_item_matrix)
        self.item_factors = svd.components_.T
        
        # Build KNN model for user-based collaborative filtering
        n_neighbors = min(10, len(self.user_factors))
        self.collaborative_model = NearestNeighbors(
            n_neighbors=n_neighbors, 
            metric='cosine'
        )
        self.collaborative_model.fit(self.user_factors)
        
        print("Collaborative filtering model built successfully")
    
    def get_content_recommendations(self, destination_id, n_recommendations=5):
        """Get content-based recommendations"""
        try:
            # Find destination index
            dest_idx = self.destinations_df[
                self.destinations_df['DestinationID'] == destination_id
            ].index[0]
            
            # Get similarity scores
            sim_scores = list(enumerate(self.content_similarity[dest_idx]))
            sim_scores = sorted(sim_scores, key=lambda x: x[1], reverse=True)
            
            # Get top recommendations (excluding the destination itself)
            recommendations = []
            for i, score in sim_scores[1:n_recommendations+1]:
                dest_info = self.destinations_df.iloc[i]
                recommendations.append({
                    'DestinationID': dest_info['DestinationID'],
                    'Name': dest_info['Name'],
                    'State': dest_info['State'],
                    'Type': dest_info['Type'],
                    'Popularity': dest_info['Popularity'],
                    'similarity_score': score
                })
            
            return recommendations
        except Exception as e:
            print(f"Error in content recommendations: {e}")
            return []
    
    def get_collaborative_recommendations(self, user_id, n_recommendations=5):
        """Get collaborative filtering recommendations"""
        try:
            if user_id not in self.user_item_matrix.index:
                return []
            
            # Get user index
            user_idx = list(self.user_item_matrix.index).index(user_id)
            user_vector = self.user_factors[user_idx].reshape(1, -1)
            
            # Find similar users
            distances, indices = self.collaborative_model.kneighbors(user_vector)
            
            # Get recommendations based on similar users
            similar_users = [list(self.user_item_matrix.index)[i] for i in indices[0][1:]]
            
            # Find destinations liked by similar users but not rated by current user
            user_ratings = self.user_item_matrix.loc[user_id]
            unrated_destinations = user_ratings[user_ratings == 0].index
            
            recommendations = []
            for dest_id in unrated_destinations[:n_recommendations]:
                # Calculate predicted rating based on similar users
                similar_ratings = []
                for sim_user in similar_users:
                    if self.user_item_matrix.loc[sim_user, dest_id] > 0:
                        similar_ratings.append(self.user_item_matrix.loc[sim_user, dest_id])
                
                if similar_ratings:
                    predicted_rating = np.mean(similar_ratings)
                    dest_info = self.destinations_df[
                        self.destinations_df['DestinationID'] == dest_id
                    ].iloc[0]
                    
                    recommendations.append({
                        'DestinationID': dest_id,
                        'Name': dest_info['Name'],
                        'State': dest_info['State'],
                        'Type': dest_info['Type'],
                        'Popularity': dest_info['Popularity'],
                        'predicted_rating': predicted_rating
                    })
            
            # Sort by predicted rating
            recommendations = sorted(
                recommendations, 
                key=lambda x: x['predicted_rating'], 
                reverse=True
            )
            
            return recommendations[:n_recommendations]
        except Exception as e:
            print(f"Error in collaborative recommendations: {e}")
            return []
    
    def get_cluster_recommendations(self, user_preferences, n_recommendations=5):
        """Get cluster-based recommendations"""
        try:
            # Find destinations based on user preferences
            preferred_type = user_preferences.get('type', 'City')
            preferred_state = user_preferences.get('state', 'Goa')
            
            # Find similar destinations by preferences
            similar_destinations = self.destinations_df[
                (self.destinations_df['Type'] == preferred_type) |
                (self.destinations_df['State'] == preferred_state)
            ].sort_values('Popularity', ascending=False)
            
            recommendations = []
            for _, dest in similar_destinations.head(n_recommendations).iterrows():
                recommendations.append({
                    'DestinationID': dest['DestinationID'],
                    'Name': dest['Name'],
                    'State': dest['State'],
                    'Type': dest['Type'],
                    'Popularity': dest['Popularity']
                })
            
            return recommendations
        except Exception as e:
            print(f"Error in cluster recommendations: {e}")
            return []
    
    def get_hybrid_recommendations(self, user_id=None, destination_id=None, 
                                 user_preferences=None, n_recommendations=5):
        """Get hybrid recommendations combining multiple approaches"""
        all_recommendations = []
        
        # Content-based recommendations
        if destination_id:
            content_recs = self.get_content_recommendations(destination_id, n_recommendations)
            for rec in content_recs:
                rec['source'] = 'content'
                rec['score'] = rec.get('similarity_score', 0) * 0.4
            all_recommendations.extend(content_recs)
        
        # Collaborative filtering recommendations
        if user_id:
            collab_recs = self.get_collaborative_recommendations(user_id, n_recommendations)
            for rec in collab_recs:
                rec['source'] = 'collaborative'
                rec['score'] = rec.get('predicted_rating', 0) * 0.4
            all_recommendations.extend(collab_recs)
        
        # Cluster-based recommendations
        if user_preferences:
            cluster_recs = self.get_cluster_recommendations(user_preferences, n_recommendations)
            for rec in cluster_recs:
                rec['source'] = 'cluster'
                rec['score'] = rec.get('Popularity', 0) * 0.2
            all_recommendations.extend(cluster_recs)
        
        # Remove duplicates and sort by score
        unique_recs = {}
        for rec in all_recommendations:
            dest_id = rec['DestinationID']
            if dest_id not in unique_recs or rec['score'] > unique_recs[dest_id]['score']:
                unique_recs[dest_id] = rec
        
        # Sort by score and return top recommendations
        final_recs = sorted(
            unique_recs.values(), 
            key=lambda x: x.get('score', 0), 
            reverse=True
        )
        
        return final_recs[:n_recommendations]
    
    def train_all_models(self):
        """Train all recommendation models"""
        print("=== Training Recommendation Models ===")
        
        if not self.load_data():
            return False
        
        try:
            self.build_content_based_model()
            self.build_collaborative_model()
            
            print("=== All Models Trained Successfully ===")
            return True
        except Exception as e:
            print(f"Error training models: {e}")
            return False

if __name__ == "__main__":
    # Initialize and train the recommendation system
    rec_system = TouristRecommendationSystem()
    
    if rec_system.train_all_models():
        print("\nRecommendation system ready!")
        
        # Example usage
        print("\n=== Example Recommendations ===")
        
        # Content-based example
        content_recs = rec_system.get_content_recommendations(1, 3)
        print(f"Content-based recommendations: {len(content_recs)} found")
        for i, rec in enumerate(content_recs, 1):
            print(f"  {i}. {rec['Name']} ({rec['State']}) - Score: {rec.get('similarity_score', 0):.3f}")
        
        # Collaborative filtering example
        collab_recs = rec_system.get_collaborative_recommendations(1, 3)
        print(f"\nCollaborative recommendations: {len(collab_recs)} found")
        for i, rec in enumerate(collab_recs, 1):
            print(f"  {i}. {rec['Name']} ({rec['State']}) - Rating: {rec.get('predicted_rating', 0):.2f}")
        
        # Hybrid example
        user_prefs = {'type': 'Beach', 'state': 'Goa'}
        hybrid_recs = rec_system.get_hybrid_recommendations(
            user_preferences=user_prefs, 
            n_recommendations=3
        )
        print(f"\nHybrid recommendations: {len(hybrid_recs)} found")
        for i, rec in enumerate(hybrid_recs, 1):
            print(f"  {i}. {rec['Name']} ({rec['State']}) - Source: {rec.get('source', 'N/A')}")
        
    else:
        print("Failed to train recommendation system")