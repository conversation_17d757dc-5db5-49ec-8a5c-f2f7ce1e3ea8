"""
Script to train and save the XGBoost model for tourist rating prediction
Based on the analysis from Tourists_EDA.ipynb
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from textblob import TextBlob
import xgboost as xgb
from xgboost import XGBClassifier
import joblib
import warnings
warnings.filterwarnings('ignore')

def get_sentiment_score(text):
    """Calculate sentiment score using TextBlob"""
    if pd.isna(text) or text == "":
        return 0.0
    blob = TextBlob(str(text))
    return blob.sentiment.polarity

def prepare_data():
    """Load and prepare the data for training"""
    print("Loading data...")
    
    # Load the cleaned data
    df = pd.read_csv('cleaned_data.csv')
    print(f"Data loaded: {df.shape}")
    
    # Calculate sentiment scores for review text
    print("Calculating sentiment scores...")
    df['SentimentScore'] = df['ReviewText'].apply(get_sentiment_score)
    
    # Create one-hot encoded features
    print("Creating one-hot encoded features...")
    
    # One-hot encode categorical variables
    df_ohe = pd.get_dummies(df, columns=['State', 'Type', 'BestTimeToVisit', 'Gender', 'Preferences'])
    
    print(f"Data after one-hot encoding: {df_ohe.shape}")
    print("Columns:", df_ohe.columns.tolist())
    
    return df_ohe

def select_features(df_ohe):
    """Select features for the model based on notebook analysis"""
    
    # Define target variable
    target_variable = 'Rating'
    
    # Exclude non-feature columns
    exclude_cols = ['ReviewID', 'DestinationID_x', 'UserID', 'ReviewText', 
                   'Name_x', 'Name_y', 'Email', 'HistoryID', 'DestinationID_y', 
                   'VisitDate', target_variable]
    
    # Get all feature columns
    all_features = [col for col in df_ohe.columns if col not in exclude_cols]
    
    # Selected features based on permutation importance from notebook
    selected_features = [
        'Type_City', 'Type_Beach', 'State_Uttar Pradesh', 'Gender_Male', 
        'Type_Historical', 'State_Rajasthan', 'State_Goa', 
        'Preferences_City, Historical', 'State_Kerala', 
        'Preferences_Nature, Adventure', 'State_Jammu and Kashmir', 
        'Preferences_Beaches, Historical', 'NumberOfAdults', 
        'SentimentScore', 'Gender_Female', 'NumberOfChildren', 'Popularity'
    ]
    
    # Filter to only include features that exist in the data
    available_features = [f for f in selected_features if f in df_ohe.columns]
    
    print(f"Selected features ({len(available_features)}):")
    for feature in available_features:
        print(f"  - {feature}")
    
    # Prepare feature matrix and target
    X = df_ohe[available_features]
    y = df_ohe[target_variable].astype(int)
    
    return X, y, available_features

def train_model(X, y):
    """Train the XGBoost model"""
    print("\nSplitting data...")
    
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"Training set: {X_train.shape}")
    print(f"Test set: {X_test.shape}")
    
    # Convert target to 0-indexed for XGBoost
    y_train_xgb = y_train - 1
    y_test_xgb = y_test - 1
    
    print("\nTraining XGBoost model...")
    
    # Create and train the model with best parameters from notebook
    model = XGBClassifier(
        objective='multi:softmax',
        num_class=len(np.unique(y_train_xgb)),
        random_state=42,
        eval_metric='mlogloss',
        # Best parameters from grid search (you can adjust these)
        n_estimators=200,
        learning_rate=0.1,
        max_depth=4,
        subsample=0.9,
        colsample_bytree=0.9
    )
    
    model.fit(X_train, y_train_xgb)
    
    print("Model training complete!")
    
    # Evaluate the model
    from sklearn.metrics import accuracy_score, classification_report
    
    y_pred = model.predict(X_test) + 1  # Convert back to 1-indexed
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"\nModel Performance:")
    print(f"Accuracy: {accuracy:.4f}")
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred))
    
    return model, X_test, y_test

def save_model(model, feature_names):
    """Save the trained model and feature names"""
    print("\nSaving model...")
    
    # Save the model
    joblib.dump(model, 'xgboost_model.pkl')
    
    # Save feature names for reference
    joblib.dump(feature_names, 'feature_names.pkl')
    
    print("Model saved as 'xgboost_model.pkl'")
    print("Feature names saved as 'feature_names.pkl'")

def main():
    """Main training pipeline"""
    print("=== Tourist Rating Prediction Model Training ===\n")
    
    try:
        # Prepare data
        df_ohe = prepare_data()
        
        # Select features
        X, y, feature_names = select_features(df_ohe)
        
        # Train model
        model, X_test, y_test = train_model(X, y)
        
        # Save model
        save_model(model, feature_names)
        
        print("\n=== Training Complete! ===")
        print("You can now use the saved model in your Streamlit app.")
        
    except Exception as e:
        print(f"Error during training: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
