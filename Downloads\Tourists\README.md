# 🏖️ Tourist Destination Rating Predictor

A Streamlit web application that predicts tourist destination ratings using machine learning. Built with XGBoost and based on comprehensive exploratory data analysis.

## 🚀 Features

- **Interactive Web Interface**: User-friendly Streamlit interface for inputting destination and user information
- **Machine Learning Prediction**: XGBoost classifier trained on tourist data with 62.31% accuracy
- **Feature Engineering**: Includes sentiment analysis, one-hot encoding, and feature selection
- **Real-time Predictions**: Instant rating predictions (1-5 stars) based on user inputs
- **Comprehensive Analysis**: Displays feature importance and model insights

## 📊 Model Details

- **Algorithm**: XGBoost Classifier with hyperparameter tuning
- **Features**: 17 selected features based on permutation importance
- **Accuracy**: 62.31% on test data
- **Target**: Tourist rating prediction (1-5 stars)

### Key Features Used:
- Destination type (City, Beach, Historical, Nature, Adventure)
- State (Goa, Kerala, Rajasthan, Uttar Pradesh, Jammu and Kashmir)
- User preferences and demographics
- Destination popularity
- Sentiment analysis of review text
- Travel group composition

## 🛠️ Installation

### Quick Start (Recommended)
1. **Run the setup script**:
   ```bash
   python setup_environment.py
   ```
   Choose option 1 to create a new virtual environment with compatible packages.

### Manual Installation
If you encounter NumPy compatibility issues:

**Option A: Create Virtual Environment**
```bash
python -m venv tourist_env
tourist_env\Scripts\activate  # Windows
source tourist_env/bin/activate  # Mac/Linux
pip install numpy==1.24.3 pandas==1.5.3 scikit-learn==1.3.2
pip install xgboost==1.7.6 streamlit textblob joblib
```

**Option B: Fix Current Environment**
```bash
pip install numpy<2.0 --force-reinstall
pip install -r requirements.txt
```

### Running the Application
1. **Run the Streamlit app**:
   ```bash
   streamlit run app.py
   ```

2. **Train the model** (optional):
   ```bash
   python train_model.py
   ```
   Note: The app works with intelligent mock predictions even without training!

## 📁 Project Structure

```
├── app.py                 # Main Streamlit application
├── train_model.py         # Model training script
├── setup_environment.py   # Environment setup script
├── requirements.txt       # Python dependencies
├── cleaned_data.csv       # Training data (or uses mock data)
├── Tourists_EDA.ipynb     # Exploratory data analysis notebook
├── xgboost_model.pkl      # Trained model (generated after training)
├── feature_names.pkl      # Feature names (generated after training)
└── README.md             # This file
```

## 🎯 Usage

1. **Start the application**: Run `streamlit run app.py`
2. **Input destination details**:
   - Select state and destination type
   - Choose best time to visit
   - Set destination popularity score

3. **Provide user information**:
   - Gender and travel preferences
   - Number of adults and children
   - Previous experience rating

4. **Add review text**: Enter review text for sentiment analysis
5. **Get prediction**: Click "Predict Rating" to get the predicted rating

## 📈 Model Performance

The XGBoost model achieves:
- **Overall Accuracy**: 62.31%
- **Precision**: Varies by rating class (0.57-0.72)
- **Recall**: Varies by rating class (0.49-0.80)
- **F1-Score**: Balanced performance across rating classes

## 🔧 Technical Details

### Data Processing:
- One-hot encoding for categorical variables
- Sentiment analysis using TextBlob
- Feature selection based on permutation importance
- Stratified train-test split

### Model Configuration:
- XGBoost multi-class classifier
- Hyperparameter tuning with GridSearchCV
- Cross-validation for model selection
- Feature importance analysis

## 🚀 Deployment

For production deployment:

1. **Cloud Platforms**: Deploy on Streamlit Cloud, Heroku, or AWS
2. **Docker**: Containerize the application
3. **Model Versioning**: Use MLflow or similar for model management
4. **Monitoring**: Implement prediction monitoring and model drift detection

## 📝 Data Requirements

The model expects the following input features:
- **Categorical**: State, Type, Gender, Preferences, BestTimeToVisit
- **Numerical**: Popularity, NumberOfAdults, NumberOfChildren, ExperienceRating
- **Text**: ReviewText (for sentiment analysis)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- Built using Streamlit for the web interface
- XGBoost for machine learning predictions
- TextBlob for sentiment analysis
- Based on tourist destination and review data analysis

## 📞 Support

For questions or issues:
1. Check the troubleshooting section below
2. Review the code comments
3. Create an issue in the repository

## 🔧 Troubleshooting

**Model not found error**:
- Run `python train_model.py` to train the model first
- Ensure `cleaned_data.csv` is in the project directory

**Import errors**:
- Install all dependencies: `pip install -r requirements.txt`
- Check Python version compatibility (3.7+)

**Data loading issues**:
- Verify `cleaned_data.csv` exists and is properly formatted
- Check file permissions and path

**Prediction errors**:
- Ensure model files (`xgboost_model.pkl`, `feature_names.pkl`) exist
- Verify input data format matches training data
