# 🏖️ Tourist Destination Rating Predictor

A Streamlit web application that predicts tourist destination ratings using a trained XGBoost machine learning model.

## 📁 Project Files

### Essential Files
- **`app.py`** - Main Streamlit application
- **`train_model.py`** - XGBoost model training script
- **`cleaned_data.csv`** - Training dataset
- **`requirements.txt`** - Python dependencies
- **`Tourists_EDA.ipynb`** - Exploratory data analysis notebook

### Data Files
- **`Expanded_Destinations.csv`** - Destination information
- **`Final_Updated_Expanded_Reviews.csv`** - Tourist reviews
- **`Final_Updated_Expanded_UserHistory.csv`** - User history
- **`Final_Updated_Expanded_Users.csv`** - User information

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Train the Model
```bash
python train_model.py
```
This creates:
- `xgboost_model.pkl` - Trained XGBoost model
- `feature_names.pkl` - Feature names for the model

### 3. Run the Application
```bash
streamlit run app.py
```

## 🎯 Features

### Input Parameters
- **Destination Info**: State, Type, Best Time to Visit, Popularity
- **User Info**: Gender, Preferences, Group Size (Adults/Children)
- **Experience**: Previous ratings, Review text for sentiment analysis

### Model Details
- **Algorithm**: XGBoost Classifier
- **Features**: 17 selected features based on permutation importance
- **Target**: Tourist rating prediction (1-5 stars)
- **Accuracy**: ~62% on test data

### Key Features Used
- Destination popularity and type
- User preferences and demographics
- Sentiment analysis of review text
- Group composition (adults/children)
- State and seasonal factors

## 📁 Project Structure

```
├── app.py                 # Main Streamlit application
├── train_model.py         # Model training script
├── setup_environment.py   # Environment setup script
├── requirements.txt       # Python dependencies
├── cleaned_data.csv       # Training data (or uses mock data)
├── Tourists_EDA.ipynb     # Exploratory data analysis notebook
├── xgboost_model.pkl      # Trained model (generated after training)
├── feature_names.pkl      # Feature names (generated after training)
└── README.md             # This file
```

## 🎯 Usage

1. **Start the application**: Run `streamlit run app.py`
2. **Input destination details**:
   - Select state and destination type
   - Choose best time to visit
   - Set destination popularity score

3. **Provide user information**:
   - Gender and travel preferences
   - Number of adults and children
   - Previous experience rating

4. **Add review text**: Enter review text for sentiment analysis
5. **Get prediction**: Click "Predict Rating" to get the predicted rating

## 📈 Model Performance

The XGBoost model achieves:
- **Overall Accuracy**: 62.31%
- **Precision**: Varies by rating class (0.57-0.72)
- **Recall**: Varies by rating class (0.49-0.80)
- **F1-Score**: Balanced performance across rating classes

## 🔧 Technical Details

### Data Processing:
- One-hot encoding for categorical variables
- Sentiment analysis using TextBlob
- Feature selection based on permutation importance
- Stratified train-test split

### Model Configuration:
- XGBoost multi-class classifier
- Hyperparameter tuning with GridSearchCV
- Cross-validation for model selection
- Feature importance analysis

## 🚀 Deployment

For production deployment:

1. **Cloud Platforms**: Deploy on Streamlit Cloud, Heroku, or AWS
2. **Docker**: Containerize the application
3. **Model Versioning**: Use MLflow or similar for model management
4. **Monitoring**: Implement prediction monitoring and model drift detection

## 📝 Data Requirements

The model expects the following input features:
- **Categorical**: State, Type, Gender, Preferences, BestTimeToVisit
- **Numerical**: Popularity, NumberOfAdults, NumberOfChildren, ExperienceRating
- **Text**: ReviewText (for sentiment analysis)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- Built using Streamlit for the web interface
- XGBoost for machine learning predictions
- TextBlob for sentiment analysis
- Based on tourist destination and review data analysis

## 📞 Support

For questions or issues:
1. Check the troubleshooting section below
2. Review the code comments
3. Create an issue in the repository

## 🔧 Troubleshooting

**Model not found error**:
- Run `python train_model.py` to train the model first
- Ensure `cleaned_data.csv` is in the project directory

**Import errors**:
- Install all dependencies: `pip install -r requirements.txt`
- Check Python version compatibility (3.7+)

**Data loading issues**:
- Verify `cleaned_data.csv` exists and is properly formatted
- Check file permissions and path

**Prediction errors**:
- Ensure model files (`xgboost_model.pkl`, `feature_names.pkl`) exist
- Verify input data format matches training data
